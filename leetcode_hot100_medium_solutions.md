# LeetCode Hot 100 中等难度题解

本文档包含LeetCode Hot 100中经典中等难度题目的简洁题解，每题提供问题描述和带注释的最优解法。

## 1. 两数相加 (Add Two Numbers) - 题目2

**问题描述：**
给你两个非空的链表，表示两个非负的整数。它们每位数字都是按照逆序的方式存储的，并且每个节点只能存储一位数字。请你将两个数相加，并以相同形式返回一个表示和的链表。

```python
# Definition for singly-linked list.
class ListNode:
    def __init__(self, val=0, next=None):
        self.val = val
        self.next = next

def addTwoNumbers(l1, l2):
    """
    时间复杂度: O(max(m,n))，空间复杂度: O(max(m,n))
    """
    dummy = ListNode(0)  # 创建虚拟头节点，简化边界处理
    current = dummy
    carry = 0  # 进位标志
    
    # 遍历两个链表，直到都为空且无进位
    while l1 or l2 or carry:
        # 获取当前位的值，如果链表为空则为0
        val1 = l1.val if l1 else 0
        val2 = l2.val if l2 else 0
        
        # 计算当前位的和
        total = val1 + val2 + carry
        carry = total // 10  # 新的进位
        digit = total % 10   # 当前位的数字
        
        # 创建新节点
        current.next = ListNode(digit)
        current = current.next
        
        # 移动到下一位
        if l1: l1 = l1.next
        if l2: l2 = l2.next
    
    return dummy.next  # 返回真正的头节点
```

## 2. 无重复字符的最长子串 (Longest Substring Without Repeating Characters) - 题目3

**问题描述：**
给定一个字符串s，请你找出其中不含有重复字符的最长子串的长度。

```python
def lengthOfLongestSubstring(s):
    """
    滑动窗口法
    时间复杂度: O(n)，空间复杂度: O(min(m,n))，m是字符集大小
    """
    char_map = {}  # 记录字符最后出现的位置
    left = 0       # 滑动窗口左边界
    max_len = 0    # 最长子串长度
    
    for right in range(len(s)):
        # 如果当前字符已存在且在当前窗口内
        if s[right] in char_map and char_map[s[right]] >= left:
            left = char_map[s[right]] + 1  # 移动左边界
        
        char_map[s[right]] = right  # 更新字符位置
        max_len = max(max_len, right - left + 1)  # 更新最大长度
    
    return max_len
```

## 3. 最长回文子串 (Longest Palindromic Substring) - 题目5

**问题描述：**
给你一个字符串s，找到s中最长的回文子串。

```python
def longestPalindrome(s):
    """
    中心扩展法
    时间复杂度: O(n²)，空间复杂度: O(1)
    """
    if not s:
        return ""
    
    start = 0      # 最长回文串的起始位置
    max_len = 1    # 最长回文串的长度
    
    def expand_around_center(left, right):
        """以left和right为中心向外扩展"""
        while left >= 0 and right < len(s) and s[left] == s[right]:
            left -= 1
            right += 1
        return right - left - 1  # 返回回文串长度
    
    for i in range(len(s)):
        # 检查奇数长度的回文串（以i为中心）
        len1 = expand_around_center(i, i)
        # 检查偶数长度的回文串（以i和i+1为中心）
        len2 = expand_around_center(i, i + 1)
        
        # 更新最长回文串信息
        current_max = max(len1, len2)
        if current_max > max_len:
            max_len = current_max
            start = i - (current_max - 1) // 2
    
    return s[start:start + max_len]
```

## 4. 盛最多水的容器 (Container With Most Water) - 题目11

**问题描述：**
给定一个长度为n的整数数组height。有n条垂线，第i条线的两个端点是(i, 0)和(i, height[i])。找出其中的两条线，使得它们与x轴共同构成的容器可以容纳最多的水。

```python
def maxArea(height):
    """
    双指针法
    时间复杂度: O(n)，空间复杂度: O(1)
    """
    left = 0                    # 左指针
    right = len(height) - 1     # 右指针
    max_water = 0               # 最大水量
    
    while left < right:
        # 计算当前容器的水量：宽度 × 较短的高度
        width = right - left
        current_height = min(height[left], height[right])
        current_water = width * current_height
        max_water = max(max_water, current_water)
        
        # 移动较短的那一边的指针
        # 因为移动较高的一边不可能得到更大的面积
        if height[left] < height[right]:
            left += 1
        else:
            right -= 1
    
    return max_water
```

## 5. 三数之和 (3Sum) - 题目15

**问题描述：**
给你一个包含n个整数的数组nums，判断nums中是否存在三个元素a，b，c，使得a + b + c = 0？请你找出所有和为0且不重复的三元组。

```python
def threeSum(nums):
    """
    排序 + 双指针法
    时间复杂度: O(n²)，空间复杂度: O(1)
    """
    nums.sort()  # 先排序，便于去重和使用双指针
    result = []
    n = len(nums)
    
    for i in range(n - 2):  # 第一个数的索引，最多到n-3
        # 跳过重复的第一个数
        if i > 0 and nums[i] == nums[i - 1]:
            continue
        
        # 如果最小的三个数之和都大于0，后面不可能有解
        if nums[i] + nums[i + 1] + nums[i + 2] > 0:
            break
        
        # 如果当前数与最大的两个数之和都小于0，当前数太小
        if nums[i] + nums[n - 2] + nums[n - 1] < 0:
            continue
        
        left = i + 1      # 左指针
        right = n - 1     # 右指针
        
        while left < right:
            current_sum = nums[i] + nums[left] + nums[right]
            
            if current_sum == 0:
                result.append([nums[i], nums[left], nums[right]])
                
                # 跳过重复的数字
                while left < right and nums[left] == nums[left + 1]:
                    left += 1
                while left < right and nums[right] == nums[right - 1]:
                    right -= 1
                
                left += 1
                right -= 1
            elif current_sum < 0:
                left += 1   # 和太小，左指针右移
            else:
                right -= 1  # 和太大，右指针左移
    
    return result
```

## 6. 电话号码的字母组合 (Letter Combinations of a Phone Number) - 题目17

**问题描述：**
给定一个仅包含数字2-9的字符串，返回所有它能表示的字母组合。答案可以按任意顺序返回。

```python
def letterCombinations(digits):
    """
    回溯法
    时间复杂度: O(3^m × 4^n)，空间复杂度: O(3^m × 4^n)
    m是对应3个字母的数字个数，n是对应4个字母的数字个数
    """
    if not digits:
        return []

    # 数字到字母的映射
    phone_map = {
        '2': 'abc', '3': 'def', '4': 'ghi', '5': 'jkl',
        '6': 'mno', '7': 'pqrs', '8': 'tuv', '9': 'wxyz'
    }

    result = []

    def backtrack(index, current_combination):
        """回溯函数"""
        # 如果当前组合长度等于输入数字长度，说明找到一个解
        if index == len(digits):
            result.append(current_combination)
            return

        # 获取当前数字对应的字母
        current_digit = digits[index]
        letters = phone_map[current_digit]

        # 尝试每个字母
        for letter in letters:
            backtrack(index + 1, current_combination + letter)

    backtrack(0, "")
    return result
```

## 7. 删除链表的倒数第N个结点 (Remove Nth Node From End of List) - 题目19

**问题描述：**
给你一个链表，删除链表的倒数第n个结点，并且返回链表的头结点。

```python
def removeNthFromEnd(head, n):
    """
    双指针法（快慢指针）
    时间复杂度: O(L)，空间复杂度: O(1)，L是链表长度
    """
    # 创建虚拟头节点，简化边界处理
    dummy = ListNode(0)
    dummy.next = head

    # 快指针先走n+1步
    fast = dummy
    for _ in range(n + 1):
        fast = fast.next

    # 快慢指针同时移动，直到快指针到达末尾
    slow = dummy
    while fast:
        fast = fast.next
        slow = slow.next

    # 删除倒数第n个节点
    slow.next = slow.next.next

    return dummy.next
```

## 8. 有效的括号 (Valid Parentheses) - 题目20

**问题描述：**
给定一个只包括'('，')'，'{'，'}'，'['，']'的字符串s，判断字符串是否有效。

```python
def isValid(s):
    """
    栈的应用
    时间复杂度: O(n)，空间复杂度: O(n)
    """
    # 括号映射
    mapping = {')': '(', '}': '{', ']': '['}
    stack = []

    for char in s:
        if char in mapping:  # 遇到右括号
            # 检查栈是否为空或栈顶元素是否匹配
            if not stack or stack.pop() != mapping[char]:
                return False
        else:  # 遇到左括号
            stack.append(char)

    # 最后栈应该为空
    return not stack
```

## 9. 合并两个有序链表 (Merge Two Sorted Lists) - 题目21

**问题描述：**
将两个升序链表合并为一个新的升序链表并返回。新链表是通过拼接给定的两个链表的所有节点组成的。

```python
def mergeTwoLists(list1, list2):
    """
    迭代法
    时间复杂度: O(n + m)，空间复杂度: O(1)
    """
    # 创建虚拟头节点
    dummy = ListNode(0)
    current = dummy

    # 比较两个链表的节点值，选择较小的
    while list1 and list2:
        if list1.val <= list2.val:
            current.next = list1
            list1 = list1.next
        else:
            current.next = list2
            list2 = list2.next
        current = current.next

    # 连接剩余的节点
    current.next = list1 if list1 else list2

    return dummy.next
```

## 10. 括号生成 (Generate Parentheses) - 题目22

**问题描述：**
数字n代表生成括号的对数，请你设计一个函数，用于能够生成所有可能的并且有效的括号组合。

```python
def generateParenthesis(n):
    """
    回溯法
    时间复杂度: O(4^n/√n)，空间复杂度: O(4^n/√n)
    """
    result = []

    def backtrack(current, open_count, close_count):
        """
        current: 当前构建的字符串
        open_count: 已使用的左括号数量
        close_count: 已使用的右括号数量
        """
        # 如果字符串长度达到2n，说明找到一个有效组合
        if len(current) == 2 * n:
            result.append(current)
            return

        # 如果左括号数量小于n，可以添加左括号
        if open_count < n:
            backtrack(current + '(', open_count + 1, close_count)

        # 如果右括号数量小于左括号数量，可以添加右括号
        if close_count < open_count:
            backtrack(current + ')', open_count, close_count + 1)

    backtrack('', 0, 0)
    return result
```

## 11. 下一个排列 (Next Permutation) - 题目31

**问题描述：**
实现获取下一个排列的函数，算法需要将给定数字序列重新排列成字典序中下一个更大的排列。

```python
def nextPermutation(nums):
    """
    两遍扫描法
    时间复杂度: O(n)，空间复杂度: O(1)
    """
    n = len(nums)

    # 第一步：从右往左找第一个升序对 (i, i+1)
    i = n - 2
    while i >= 0 and nums[i] >= nums[i + 1]:
        i -= 1

    if i >= 0:  # 如果找到了升序对
        # 第二步：从右往左找第一个大于nums[i]的数
        j = n - 1
        while nums[j] <= nums[i]:
            j -= 1
        # 交换nums[i]和nums[j]
        nums[i], nums[j] = nums[j], nums[i]

    # 第三步：反转i+1到末尾的部分
    nums[i + 1:] = reversed(nums[i + 1:])
```

## 12. 搜索旋转排序数组 (Search in Rotated Sorted Array) - 题目33

**问题描述：**
整数数组nums按升序排列，数组中的值互不相同。在传递给函数之前，nums在预先未知的某个下标k上进行了旋转。给你旋转后的数组nums和一个整数target，如果nums中存在这个目标值target，则返回它的下标，否则返回-1。

```python
def search(nums, target):
    """
    二分查找
    时间复杂度: O(log n)，空间复杂度: O(1)
    """
    left, right = 0, len(nums) - 1

    while left <= right:
        mid = (left + right) // 2

        if nums[mid] == target:
            return mid

        # 判断哪一半是有序的
        if nums[left] <= nums[mid]:  # 左半部分有序
            if nums[left] <= target < nums[mid]:
                right = mid - 1  # target在左半部分
            else:
                left = mid + 1   # target在右半部分
        else:  # 右半部分有序
            if nums[mid] < target <= nums[right]:
                left = mid + 1   # target在右半部分
            else:
                right = mid - 1  # target在左半部分

    return -1
```

## 13. 在排序数组中查找元素的第一个和最后一个位置 (Find First and Last Position of Element in Sorted Array) - 题目34

**问题描述：**
给定一个按照升序排列的整数数组nums，和一个目标值target。找出给定目标值在数组中的开始位置和结束位置。

```python
def searchRange(nums, target):
    """
    二分查找的变种
    时间复杂度: O(log n)，空间复杂度: O(1)
    """
    def findFirst(nums, target):
        """找到target的第一个位置"""
        left, right = 0, len(nums) - 1
        while left <= right:
            mid = (left + right) // 2
            if nums[mid] < target:
                left = mid + 1
            else:
                right = mid - 1
        return left if left < len(nums) and nums[left] == target else -1

    def findLast(nums, target):
        """找到target的最后一个位置"""
        left, right = 0, len(nums) - 1
        while left <= right:
            mid = (left + right) // 2
            if nums[mid] <= target:
                left = mid + 1
            else:
                right = mid - 1
        return right if right >= 0 and nums[right] == target else -1

    first = findFirst(nums, target)
    if first == -1:
        return [-1, -1]

    last = findLast(nums, target)
    return [first, last]
```

## 14. 组合总和 (Combination Sum) - 题目39

**问题描述：**
给你一个无重复元素的整数数组candidates和一个目标整数target，找出candidates中可以使数字和为目标数target的所有不同组合。

```python
def combinationSum(candidates, target):
    """
    回溯法
    时间复杂度: O(S)，空间复杂度: O(target)
    S为所有可行解的长度之和
    """
    result = []

    def backtrack(start, current_combination, remaining):
        """
        start: 开始搜索的索引
        current_combination: 当前组合
        remaining: 剩余需要的和
        """
        if remaining == 0:  # 找到一个有效组合
            result.append(current_combination[:])  # 复制当前组合
            return

        for i in range(start, len(candidates)):
            if candidates[i] <= remaining:  # 剪枝：如果当前数字大于剩余和，跳过
                current_combination.append(candidates[i])
                # 可以重复使用同一个数字，所以下次搜索从i开始
                backtrack(i, current_combination, remaining - candidates[i])
                current_combination.pop()  # 回溯

    backtrack(0, [], target)
    return result
```

## 15. 接雨水 (Trapping Rain Water) - 题目42

**问题描述：**
给定n个非负整数表示每个宽度为1的柱子的高度图，计算按此排列的柱子，下雨之后能够接多少雨水。

```python
def trap(height):
    """
    双指针法
    时间复杂度: O(n)，空间复杂度: O(1)
    """
    if not height:
        return 0

    left, right = 0, len(height) - 1
    left_max = right_max = 0  # 左右两边的最大高度
    water = 0

    while left < right:
        if height[left] < height[right]:
            # 左边较低，处理左边
            if height[left] >= left_max:
                left_max = height[left]  # 更新左边最大高度
            else:
                water += left_max - height[left]  # 可以接水
            left += 1
        else:
            # 右边较低，处理右边
            if height[right] >= right_max:
                right_max = height[right]  # 更新右边最大高度
            else:
                water += right_max - height[right]  # 可以接水
            right -= 1

    return water
```

## 16. 全排列 (Permutations) - 题目46

**问题描述：**
给定一个不含重复数字的数组nums，返回其所有可能的全排列。你可以按任意顺序返回答案。

```python
def permute(nums):
    """
    回溯法
    时间复杂度: O(n × n!)，空间复杂度: O(n)
    """
    result = []

    def backtrack(current_permutation):
        # 如果当前排列长度等于原数组长度，找到一个解
        if len(current_permutation) == len(nums):
            result.append(current_permutation[:])  # 复制当前排列
            return

        for num in nums:
            if num not in current_permutation:  # 避免重复使用
                current_permutation.append(num)
                backtrack(current_permutation)
                current_permutation.pop()  # 回溯

    backtrack([])
    return result
```

## 17. 旋转图像 (Rotate Image) - 题目48

**问题描述：**
给定一个n×n的二维矩阵matrix表示一个图像。请你将图像顺时针旋转90度。

```python
def rotate(matrix):
    """
    先转置，再水平翻转
    时间复杂度: O(n²)，空间复杂度: O(1)
    """
    n = len(matrix)

    # 第一步：转置矩阵（沿主对角线翻转）
    for i in range(n):
        for j in range(i, n):
            matrix[i][j], matrix[j][i] = matrix[j][i], matrix[i][j]

    # 第二步：水平翻转每一行
    for i in range(n):
        matrix[i].reverse()
```

## 18. 字母异位词分组 (Group Anagrams) - 题目49

**问题描述：**
给你一个字符串数组，请你将字母异位词组合在一起。可以按任意顺序返回结果列表。

```python
def groupAnagrams(strs):
    """
    哈希表 + 排序
    时间复杂度: O(n × k × log k)，空间复杂度: O(n × k)
    n是字符串数量，k是字符串的最大长度
    """
    from collections import defaultdict

    anagram_map = defaultdict(list)

    for s in strs:
        # 将字符串排序作为key，相同字母异位词的key相同
        key = ''.join(sorted(s))
        anagram_map[key].append(s)

    return list(anagram_map.values())
```

## 19. 最大子数组和 (Maximum Subarray) - 题目53

**问题描述：**
给你一个整数数组nums，请你找出一个具有最大和的连续子数组（子数组最少包含一个元素），返回其最大和。

```python
def maxSubArray(nums):
    """
    动态规划（Kadane算法）
    时间复杂度: O(n)，空间复杂度: O(1)
    """
    max_sum = nums[0]      # 全局最大和
    current_sum = nums[0]  # 当前子数组和

    for i in range(1, len(nums)):
        # 要么继续之前的子数组，要么重新开始
        current_sum = max(nums[i], current_sum + nums[i])
        max_sum = max(max_sum, current_sum)

    return max_sum
```

## 20. 跳跃游戏 (Jump Game) - 题目55

**问题描述：**
给定一个非负整数数组nums，你最初位于数组的第一个下标。数组中的每个元素代表你在该位置可以跳跃的最大长度。判断你是否能够到达最后一个下标。

```python
def canJump(nums):
    """
    贪心算法
    时间复杂度: O(n)，空间复杂度: O(1)
    """
    max_reach = 0  # 能到达的最远位置

    for i in range(len(nums)):
        # 如果当前位置超过了能到达的最远位置，返回False
        if i > max_reach:
            return False

        # 更新能到达的最远位置
        max_reach = max(max_reach, i + nums[i])

        # 如果已经能到达最后一个位置，返回True
        if max_reach >= len(nums) - 1:
            return True

    return True
```

---

## 总结

以上20道题目涵盖了LeetCode Hot 100中最经典的中等难度题目，包括：

- **数组与字符串**：最长子串、盛水容器、接雨水等
- **链表**：两数相加、删除倒数第N个节点、合并链表等
- **回溯算法**：电话号码组合、括号生成、全排列等
- **动态规划**：最长回文子串、最大子数组和等
- **双指针**：三数之和、接雨水等
- **二分查找**：搜索旋转数组等

每个解法都选择了最简洁高效的实现方式，并提供了详细的注释说明算法思路和复杂度分析。建议按顺序练习，掌握这些经典题目的解题模式。
```
