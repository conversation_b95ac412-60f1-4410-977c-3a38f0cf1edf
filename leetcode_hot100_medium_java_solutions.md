# LeetCode Hot 100 中等难度题解 (Java版)

本文档包含LeetCode Hot 100中所有中等难度题目的Java解法，每题提供问题描述和带详细注释的最优解法。

## 1. 两数相加 (Add Two Numbers) - 题目2

**问题描述：**
给你两个非空的链表，表示两个非负的整数。它们每位数字都是按照逆序的方式存储的，并且每个节点只能存储一位数字。请你将两个数相加，并以相同形式返回一个表示和的链表。

```java
/**
 * Definition for singly-linked list.
 */
class ListNode {
    int val;
    ListNode next;
    ListNode() {}
    ListNode(int val) { this.val = val; }
    ListNode(int val, ListNode next) { this.val = val; this.next = next; }
}

public ListNode addTwoNumbers(ListNode l1, ListNode l2) {
    /**
     * 时间复杂度: O(max(m,n))，空间复杂度: O(max(m,n))
     */
    ListNode dummy = new ListNode(0); // 创建虚拟头节点，简化边界处理
    ListNode current = dummy;
    int carry = 0; // 进位标志
    
    // 遍历两个链表，直到都为空且无进位
    while (l1 != null || l2 != null || carry != 0) {
        // 获取当前位的值，如果链表为空则为0
        int val1 = (l1 != null) ? l1.val : 0;
        int val2 = (l2 != null) ? l2.val : 0;
        
        // 计算当前位的和
        int total = val1 + val2 + carry;
        carry = total / 10;  // 新的进位
        int digit = total % 10; // 当前位的数字
        
        // 创建新节点
        current.next = new ListNode(digit);
        current = current.next;
        
        // 移动到下一位
        if (l1 != null) l1 = l1.next;
        if (l2 != null) l2 = l2.next;
    }
    
    return dummy.next; // 返回真正的头节点
}
```

## 2. 无重复字符的最长子串 (Longest Substring Without Repeating Characters) - 题目3

**问题描述：**
给定一个字符串s，请你找出其中不含有重复字符的最长子串的长度。

```java
import java.util.HashMap;
import java.util.Map;

public int lengthOfLongestSubstring(String s) {
    /**
     * 滑动窗口法
     * 时间复杂度: O(n)，空间复杂度: O(min(m,n))，m是字符集大小
     */
    Map<Character, Integer> charMap = new HashMap<>(); // 记录字符最后出现的位置
    int left = 0;     // 滑动窗口左边界
    int maxLen = 0;   // 最长子串长度
    
    for (int right = 0; right < s.length(); right++) {
        char c = s.charAt(right);
        
        // 如果当前字符已存在且在当前窗口内
        if (charMap.containsKey(c) && charMap.get(c) >= left) {
            left = charMap.get(c) + 1; // 移动左边界
        }
        
        charMap.put(c, right); // 更新字符位置
        maxLen = Math.max(maxLen, right - left + 1); // 更新最大长度
    }
    
    return maxLen;
}
```

## 3. 最长回文子串 (Longest Palindromic Substring) - 题目5

**问题描述：**
给你一个字符串s，找到s中最长的回文子串。

```java
public String longestPalindrome(String s) {
    /**
     * 中心扩展法
     * 时间复杂度: O(n²)，空间复杂度: O(1)
     */
    if (s == null || s.length() == 0) {
        return "";
    }
    
    int start = 0;    // 最长回文串的起始位置
    int maxLen = 1;   // 最长回文串的长度
    
    for (int i = 0; i < s.length(); i++) {
        // 检查奇数长度的回文串（以i为中心）
        int len1 = expandAroundCenter(s, i, i);
        // 检查偶数长度的回文串（以i和i+1为中心）
        int len2 = expandAroundCenter(s, i, i + 1);
        
        // 更新最长回文串信息
        int currentMax = Math.max(len1, len2);
        if (currentMax > maxLen) {
            maxLen = currentMax;
            start = i - (currentMax - 1) / 2;
        }
    }
    
    return s.substring(start, start + maxLen);
}

/**
 * 以left和right为中心向外扩展
 */
private int expandAroundCenter(String s, int left, int right) {
    while (left >= 0 && right < s.length() && s.charAt(left) == s.charAt(right)) {
        left--;
        right++;
    }
    return right - left - 1; // 返回回文串长度
}
```

## 4. 盛最多水的容器 (Container With Most Water) - 题目11

**问题描述：**
给定一个长度为n的整数数组height。有n条垂线，第i条线的两个端点是(i, 0)和(i, height[i])。找出其中的两条线，使得它们与x轴共同构成的容器可以容纳最多的水。

```java
public int maxArea(int[] height) {
    /**
     * 双指针法
     * 时间复杂度: O(n)，空间复杂度: O(1)
     */
    int left = 0;                    // 左指针
    int right = height.length - 1;   // 右指针
    int maxWater = 0;                // 最大水量
    
    while (left < right) {
        // 计算当前容器的水量：宽度 × 较短的高度
        int width = right - left;
        int currentHeight = Math.min(height[left], height[right]);
        int currentWater = width * currentHeight;
        maxWater = Math.max(maxWater, currentWater);
        
        // 移动较短的那一边的指针
        // 因为移动较高的一边不可能得到更大的面积
        if (height[left] < height[right]) {
            left++;
        } else {
            right--;
        }
    }
    
    return maxWater;
}
```

## 5. 三数之和 (3Sum) - 题目15

**问题描述：**
给你一个包含n个整数的数组nums，判断nums中是否存在三个元素a，b，c，使得a + b + c = 0？请你找出所有和为0且不重复的三元组。

```java
import java.util.*;

public List<List<Integer>> threeSum(int[] nums) {
    /**
     * 排序 + 双指针法
     * 时间复杂度: O(n²)，空间复杂度: O(1)
     */
    Arrays.sort(nums); // 先排序，便于去重和使用双指针
    List<List<Integer>> result = new ArrayList<>();
    int n = nums.length;
    
    for (int i = 0; i < n - 2; i++) { // 第一个数的索引，最多到n-3
        // 跳过重复的第一个数
        if (i > 0 && nums[i] == nums[i - 1]) {
            continue;
        }
        
        // 如果最小的三个数之和都大于0，后面不可能有解
        if (nums[i] + nums[i + 1] + nums[i + 2] > 0) {
            break;
        }
        
        // 如果当前数与最大的两个数之和都小于0，当前数太小
        if (nums[i] + nums[n - 2] + nums[n - 1] < 0) {
            continue;
        }
        
        int left = i + 1;    // 左指针
        int right = n - 1;   // 右指针
        
        while (left < right) {
            int currentSum = nums[i] + nums[left] + nums[right];
            
            if (currentSum == 0) {
                result.add(Arrays.asList(nums[i], nums[left], nums[right]));
                
                // 跳过重复的数字
                while (left < right && nums[left] == nums[left + 1]) {
                    left++;
                }
                while (left < right && nums[right] == nums[right - 1]) {
                    right--;
                }
                
                left++;
                right--;
            } else if (currentSum < 0) {
                left++;  // 和太小，左指针右移
            } else {
                right--; // 和太大，右指针左移
            }
        }
    }
    
    return result;
}
```

## 6. 电话号码的字母组合 (Letter Combinations of a Phone Number) - 题目17

**问题描述：**
给定一个仅包含数字2-9的字符串，返回所有它能表示的字母组合。答案可以按任意顺序返回。

```java
import java.util.*;

public List<String> letterCombinations(String digits) {
    /**
     * 回溯法
     * 时间复杂度: O(3^m × 4^n)，空间复杂度: O(3^m × 4^n)
     * m是对应3个字母的数字个数，n是对应4个字母的数字个数
     */
    if (digits == null || digits.length() == 0) {
        return new ArrayList<>();
    }

    // 数字到字母的映射
    Map<Character, String> phoneMap = new HashMap<>();
    phoneMap.put('2', "abc");
    phoneMap.put('3', "def");
    phoneMap.put('4', "ghi");
    phoneMap.put('5', "jkl");
    phoneMap.put('6', "mno");
    phoneMap.put('7', "pqrs");
    phoneMap.put('8', "tuv");
    phoneMap.put('9', "wxyz");

    List<String> result = new ArrayList<>();
    backtrack(digits, 0, new StringBuilder(), phoneMap, result);
    return result;
}

/**
 * 回溯函数
 */
private void backtrack(String digits, int index, StringBuilder current,
                      Map<Character, String> phoneMap, List<String> result) {
    // 如果当前组合长度等于输入数字长度，说明找到一个解
    if (index == digits.length()) {
        result.add(current.toString());
        return;
    }

    // 获取当前数字对应的字母
    char currentDigit = digits.charAt(index);
    String letters = phoneMap.get(currentDigit);

    // 尝试每个字母
    for (char letter : letters.toCharArray()) {
        current.append(letter);
        backtrack(digits, index + 1, current, phoneMap, result);
        current.deleteCharAt(current.length() - 1); // 回溯
    }
}
```

## 7. 删除链表的倒数第N个结点 (Remove Nth Node From End of List) - 题目19

**问题描述：**
给你一个链表，删除链表的倒数第n个结点，并且返回链表的头结点。

```java
public ListNode removeNthFromEnd(ListNode head, int n) {
    /**
     * 双指针法（快慢指针）
     * 时间复杂度: O(L)，空间复杂度: O(1)，L是链表长度
     */
    // 创建虚拟头节点，简化边界处理
    ListNode dummy = new ListNode(0);
    dummy.next = head;

    // 快指针先走n+1步
    ListNode fast = dummy;
    for (int i = 0; i <= n; i++) {
        fast = fast.next;
    }

    // 快慢指针同时移动，直到快指针到达末尾
    ListNode slow = dummy;
    while (fast != null) {
        fast = fast.next;
        slow = slow.next;
    }

    // 删除倒数第n个节点
    slow.next = slow.next.next;

    return dummy.next;
}
```

## 8. 有效的括号 (Valid Parentheses) - 题目20

**问题描述：**
给定一个只包括'('，')'，'{'，'}'，'['，']'的字符串s，判断字符串是否有效。

```java
import java.util.*;

public boolean isValid(String s) {
    /**
     * 栈的应用
     * 时间复杂度: O(n)，空间复杂度: O(n)
     */
    // 括号映射
    Map<Character, Character> mapping = new HashMap<>();
    mapping.put(')', '(');
    mapping.put('}', '{');
    mapping.put(']', '[');

    Stack<Character> stack = new Stack<>();

    for (char c : s.toCharArray()) {
        if (mapping.containsKey(c)) { // 遇到右括号
            // 检查栈是否为空或栈顶元素是否匹配
            if (stack.isEmpty() || stack.pop() != mapping.get(c)) {
                return false;
            }
        } else { // 遇到左括号
            stack.push(c);
        }
    }

    // 最后栈应该为空
    return stack.isEmpty();
}
```

## 9. 合并两个有序链表 (Merge Two Sorted Lists) - 题目21

**问题描述：**
将两个升序链表合并为一个新的升序链表并返回。新链表是通过拼接给定的两个链表的所有节点组成的。

```java
public ListNode mergeTwoLists(ListNode list1, ListNode list2) {
    /**
     * 迭代法
     * 时间复杂度: O(n + m)，空间复杂度: O(1)
     */
    // 创建虚拟头节点
    ListNode dummy = new ListNode(0);
    ListNode current = dummy;

    // 比较两个链表的节点值，选择较小的
    while (list1 != null && list2 != null) {
        if (list1.val <= list2.val) {
            current.next = list1;
            list1 = list1.next;
        } else {
            current.next = list2;
            list2 = list2.next;
        }
        current = current.next;
    }

    // 连接剩余的节点
    current.next = (list1 != null) ? list1 : list2;

    return dummy.next;
}
```

## 10. 括号生成 (Generate Parentheses) - 题目22

**问题描述：**
数字n代表生成括号的对数，请你设计一个函数，用于能够生成所有可能的并且有效的括号组合。

```java
import java.util.*;

public List<String> generateParenthesis(int n) {
    /**
     * 回溯法
     * 时间复杂度: O(4^n/√n)，空间复杂度: O(4^n/√n)
     */
    List<String> result = new ArrayList<>();
    backtrack(result, new StringBuilder(), 0, 0, n);
    return result;
}

/**
 * 回溯函数
 * @param result 结果列表
 * @param current 当前构建的字符串
 * @param openCount 已使用的左括号数量
 * @param closeCount 已使用的右括号数量
 * @param n 括号对数
 */
private void backtrack(List<String> result, StringBuilder current,
                      int openCount, int closeCount, int n) {
    // 如果字符串长度达到2n，说明找到一个有效组合
    if (current.length() == 2 * n) {
        result.add(current.toString());
        return;
    }

    // 如果左括号数量小于n，可以添加左括号
    if (openCount < n) {
        current.append('(');
        backtrack(result, current, openCount + 1, closeCount, n);
        current.deleteCharAt(current.length() - 1); // 回溯
    }

    // 如果右括号数量小于左括号数量，可以添加右括号
    if (closeCount < openCount) {
        current.append(')');
        backtrack(result, current, openCount, closeCount + 1, n);
        current.deleteCharAt(current.length() - 1); // 回溯
    }
}
```

## 11. 下一个排列 (Next Permutation) - 题目31

**问题描述：**
实现获取下一个排列的函数，算法需要将给定数字序列重新排列成字典序中下一个更大的排列。

```java
public void nextPermutation(int[] nums) {
    /**
     * 两遍扫描法
     * 时间复杂度: O(n)，空间复杂度: O(1)
     */
    int n = nums.length;

    // 第一步：从右往左找第一个升序对 (i, i+1)
    int i = n - 2;
    while (i >= 0 && nums[i] >= nums[i + 1]) {
        i--;
    }

    if (i >= 0) { // 如果找到了升序对
        // 第二步：从右往左找第一个大于nums[i]的数
        int j = n - 1;
        while (nums[j] <= nums[i]) {
            j--;
        }
        // 交换nums[i]和nums[j]
        swap(nums, i, j);
    }

    // 第三步：反转i+1到末尾的部分
    reverse(nums, i + 1, n - 1);
}

private void swap(int[] nums, int i, int j) {
    int temp = nums[i];
    nums[i] = nums[j];
    nums[j] = temp;
}

private void reverse(int[] nums, int start, int end) {
    while (start < end) {
        swap(nums, start, end);
        start++;
        end--;
    }
}
```

## 12. 搜索旋转排序数组 (Search in Rotated Sorted Array) - 题目33

**问题描述：**
整数数组nums按升序排列，数组中的值互不相同。在传递给函数之前，nums在预先未知的某个下标k上进行了旋转。给你旋转后的数组nums和一个整数target，如果nums中存在这个目标值target，则返回它的下标，否则返回-1。

```java
public int search(int[] nums, int target) {
    /**
     * 二分查找
     * 时间复杂度: O(log n)，空间复杂度: O(1)
     */
    int left = 0, right = nums.length - 1;

    while (left <= right) {
        int mid = left + (right - left) / 2;

        if (nums[mid] == target) {
            return mid;
        }

        // 判断哪一半是有序的
        if (nums[left] <= nums[mid]) { // 左半部分有序
            if (nums[left] <= target && target < nums[mid]) {
                right = mid - 1; // target在左半部分
            } else {
                left = mid + 1;  // target在右半部分
            }
        } else { // 右半部分有序
            if (nums[mid] < target && target <= nums[right]) {
                left = mid + 1;  // target在右半部分
            } else {
                right = mid - 1; // target在左半部分
            }
        }
    }

    return -1;
}
```

## 13. 在排序数组中查找元素的第一个和最后一个位置 (Find First and Last Position of Element in Sorted Array) - 题目34

**问题描述：**
给定一个按照升序排列的整数数组nums，和一个目标值target。找出给定目标值在数组中的开始位置和结束位置。

```java
public int[] searchRange(int[] nums, int target) {
    /**
     * 二分查找的变种
     * 时间复杂度: O(log n)，空间复杂度: O(1)
     */
    int first = findFirst(nums, target);
    if (first == -1) {
        return new int[]{-1, -1};
    }

    int last = findLast(nums, target);
    return new int[]{first, last};
}

/**
 * 找到target的第一个位置
 */
private int findFirst(int[] nums, int target) {
    int left = 0, right = nums.length - 1;
    while (left <= right) {
        int mid = left + (right - left) / 2;
        if (nums[mid] < target) {
            left = mid + 1;
        } else {
            right = mid - 1;
        }
    }
    return (left < nums.length && nums[left] == target) ? left : -1;
}

/**
 * 找到target的最后一个位置
 */
private int findLast(int[] nums, int target) {
    int left = 0, right = nums.length - 1;
    while (left <= right) {
        int mid = left + (right - left) / 2;
        if (nums[mid] <= target) {
            left = mid + 1;
        } else {
            right = mid - 1;
        }
    }
    return (right >= 0 && nums[right] == target) ? right : -1;
}
```

## 14. 组合总和 (Combination Sum) - 题目39

**问题描述：**
给你一个无重复元素的整数数组candidates和一个目标整数target，找出candidates中可以使数字和为目标数target的所有不同组合。

```java
import java.util.*;

public List<List<Integer>> combinationSum(int[] candidates, int target) {
    /**
     * 回溯法
     * 时间复杂度: O(S)，空间复杂度: O(target)
     * S为所有可行解的长度之和
     */
    List<List<Integer>> result = new ArrayList<>();
    backtrack(candidates, target, 0, new ArrayList<>(), result);
    return result;
}

/**
 * 回溯函数
 * @param candidates 候选数组
 * @param remaining 剩余需要的和
 * @param start 开始搜索的索引
 * @param currentCombination 当前组合
 * @param result 结果列表
 */
private void backtrack(int[] candidates, int remaining, int start,
                      List<Integer> currentCombination, List<List<Integer>> result) {
    if (remaining == 0) { // 找到一个有效组合
        result.add(new ArrayList<>(currentCombination)); // 复制当前组合
        return;
    }

    for (int i = start; i < candidates.length; i++) {
        if (candidates[i] <= remaining) { // 剪枝：如果当前数字大于剩余和，跳过
            currentCombination.add(candidates[i]);
            // 可以重复使用同一个数字，所以下次搜索从i开始
            backtrack(candidates, remaining - candidates[i], i, currentCombination, result);
            currentCombination.remove(currentCombination.size() - 1); // 回溯
        }
    }
}
```

## 15. 接雨水 (Trapping Rain Water) - 题目42

**问题描述：**
给定n个非负整数表示每个宽度为1的柱子的高度图，计算按此排列的柱子，下雨之后能够接多少雨水。

```java
public int trap(int[] height) {
    /**
     * 双指针法
     * 时间复杂度: O(n)，空间复杂度: O(1)
     */
    if (height == null || height.length == 0) {
        return 0;
    }

    int left = 0, right = height.length - 1;
    int leftMax = 0, rightMax = 0; // 左右两边的最大高度
    int water = 0;

    while (left < right) {
        if (height[left] < height[right]) {
            // 左边较低，处理左边
            if (height[left] >= leftMax) {
                leftMax = height[left]; // 更新左边最大高度
            } else {
                water += leftMax - height[left]; // 可以接水
            }
            left++;
        } else {
            // 右边较低，处理右边
            if (height[right] >= rightMax) {
                rightMax = height[right]; // 更新右边最大高度
            } else {
                water += rightMax - height[right]; // 可以接水
            }
            right--;
        }
    }

    return water;
}
```

## 16. 全排列 (Permutations) - 题目46

**问题描述：**
给定一个不含重复数字的数组nums，返回其所有可能的全排列。你可以按任意顺序返回答案。

```java
import java.util.*;

public List<List<Integer>> permute(int[] nums) {
    /**
     * 回溯法
     * 时间复杂度: O(n × n!)，空间复杂度: O(n)
     */
    List<List<Integer>> result = new ArrayList<>();
    backtrack(nums, new ArrayList<>(), result);
    return result;
}

private void backtrack(int[] nums, List<Integer> currentPermutation,
                      List<List<Integer>> result) {
    // 如果当前排列长度等于原数组长度，找到一个解
    if (currentPermutation.size() == nums.length) {
        result.add(new ArrayList<>(currentPermutation)); // 复制当前排列
        return;
    }

    for (int num : nums) {
        if (!currentPermutation.contains(num)) { // 避免重复使用
            currentPermutation.add(num);
            backtrack(nums, currentPermutation, result);
            currentPermutation.remove(currentPermutation.size() - 1); // 回溯
        }
    }
}
```

## 17. 旋转图像 (Rotate Image) - 题目48

**问题描述：**
给定一个n×n的二维矩阵matrix表示一个图像。请你将图像顺时针旋转90度。

```java
public void rotate(int[][] matrix) {
    /**
     * 先转置，再水平翻转
     * 时间复杂度: O(n²)，空间复杂度: O(1)
     */
    int n = matrix.length;

    // 第一步：转置矩阵（沿主对角线翻转）
    for (int i = 0; i < n; i++) {
        for (int j = i; j < n; j++) {
            int temp = matrix[i][j];
            matrix[i][j] = matrix[j][i];
            matrix[j][i] = temp;
        }
    }

    // 第二步：水平翻转每一行
    for (int i = 0; i < n; i++) {
        for (int j = 0; j < n / 2; j++) {
            int temp = matrix[i][j];
            matrix[i][j] = matrix[i][n - 1 - j];
            matrix[i][n - 1 - j] = temp;
        }
    }
}
```

## 18. 字母异位词分组 (Group Anagrams) - 题目49

**问题描述：**
给你一个字符串数组，请你将字母异位词组合在一起。可以按任意顺序返回结果列表。

```java
import java.util.*;

public List<List<String>> groupAnagrams(String[] strs) {
    /**
     * 哈希表 + 排序
     * 时间复杂度: O(n × k × log k)，空间复杂度: O(n × k)
     * n是字符串数量，k是字符串的最大长度
     */
    Map<String, List<String>> anagramMap = new HashMap<>();

    for (String s : strs) {
        // 将字符串排序作为key，相同字母异位词的key相同
        char[] chars = s.toCharArray();
        Arrays.sort(chars);
        String key = new String(chars);

        anagramMap.computeIfAbsent(key, k -> new ArrayList<>()).add(s);
    }

    return new ArrayList<>(anagramMap.values());
}
```

## 19. 最大子数组和 (Maximum Subarray) - 题目53

**问题描述：**
给你一个整数数组nums，请你找出一个具有最大和的连续子数组（子数组最少包含一个元素），返回其最大和。

```java
public int maxSubArray(int[] nums) {
    /**
     * 动态规划（Kadane算法）
     * 时间复杂度: O(n)，空间复杂度: O(1)
     */
    int maxSum = nums[0];     // 全局最大和
    int currentSum = nums[0]; // 当前子数组和

    for (int i = 1; i < nums.length; i++) {
        // 要么继续之前的子数组，要么重新开始
        currentSum = Math.max(nums[i], currentSum + nums[i]);
        maxSum = Math.max(maxSum, currentSum);
    }

    return maxSum;
}
```

## 20. 跳跃游戏 (Jump Game) - 题目55

**问题描述：**
给定一个非负整数数组nums，你最初位于数组的第一个下标。数组中的每个元素代表你在该位置可以跳跃的最大长度。判断你是否能够到达最后一个下标。

```java
public boolean canJump(int[] nums) {
    /**
     * 贪心算法
     * 时间复杂度: O(n)，空间复杂度: O(1)
     */
    int maxReach = 0; // 能到达的最远位置

    for (int i = 0; i < nums.length; i++) {
        // 如果当前位置超过了能到达的最远位置，返回false
        if (i > maxReach) {
            return false;
        }

        // 更新能到达的最远位置
        maxReach = Math.max(maxReach, i + nums[i]);

        // 如果已经能到达最后一个位置，返回true
        if (maxReach >= nums.length - 1) {
            return true;
        }
    }

    return true;
}
```

## 21. 合并区间 (Merge Intervals) - 题目56

**问题描述：**
以数组intervals表示若干个区间的集合，其中单个区间为intervals[i] = [starti, endi]。请你合并所有重叠的区间，并返回一个不重叠的区间数组。

```java
import java.util.*;

public int[][] merge(int[][] intervals) {
    /**
     * 排序 + 合并
     * 时间复杂度: O(n log n)，空间复杂度: O(log n)
     */
    if (intervals.length <= 1) {
        return intervals;
    }

    // 按照区间起始位置排序
    Arrays.sort(intervals, (a, b) -> Integer.compare(a[0], b[0]));

    List<int[]> merged = new ArrayList<>();
    int[] currentInterval = intervals[0];
    merged.add(currentInterval);

    for (int[] interval : intervals) {
        int currentEnd = currentInterval[1];
        int nextStart = interval[0];
        int nextEnd = interval[1];

        if (currentEnd >= nextStart) { // 有重叠
            // 合并区间
            currentInterval[1] = Math.max(currentEnd, nextEnd);
        } else { // 无重叠
            // 添加新区间
            currentInterval = interval;
            merged.add(currentInterval);
        }
    }

    return merged.toArray(new int[merged.size()][]);
}
```

## 22. 不同路径 (Unique Paths) - 题目62

**问题描述：**
一个机器人位于一个m x n网格的左上角。机器人每次只能向下或者向右移动一步。机器人试图达到网格的右下角。问总共有多少条不同的路径？

```java
public int uniquePaths(int m, int n) {
    /**
     * 动态规划
     * 时间复杂度: O(m × n)，空间复杂度: O(n)
     */
    int[] dp = new int[n];
    Arrays.fill(dp, 1); // 第一行都是1

    for (int i = 1; i < m; i++) {
        for (int j = 1; j < n; j++) {
            dp[j] += dp[j - 1]; // dp[j] = dp[j] + dp[j-1]
        }
    }

    return dp[n - 1];
}
```

## 23. 最小路径和 (Minimum Path Sum) - 题目64

**问题描述：**
给定一个包含非负整数的m x n网格grid，请找出一条从左上角到右下角的路径，使得路径上的数字总和为最小。

```java
public int minPathSum(int[][] grid) {
    /**
     * 动态规划
     * 时间复杂度: O(m × n)，空间复杂度: O(1)
     */
    int m = grid.length;
    int n = grid[0].length;

    // 初始化第一行
    for (int j = 1; j < n; j++) {
        grid[0][j] += grid[0][j - 1];
    }

    // 初始化第一列
    for (int i = 1; i < m; i++) {
        grid[i][0] += grid[i - 1][0];
    }

    // 填充其余位置
    for (int i = 1; i < m; i++) {
        for (int j = 1; j < n; j++) {
            grid[i][j] += Math.min(grid[i - 1][j], grid[i][j - 1]);
        }
    }

    return grid[m - 1][n - 1];
}
```

## 24. 爬楼梯 (Climbing Stairs) - 题目70

**问题描述：**
假设你正在爬楼梯。需要n阶你才能到达楼顶。每次你可以爬1或2个台阶。你有多少种不同的方法可以爬到楼顶呢？

```java
public int climbStairs(int n) {
    /**
     * 动态规划（斐波那契数列）
     * 时间复杂度: O(n)，空间复杂度: O(1)
     */
    if (n <= 2) {
        return n;
    }

    int prev2 = 1; // f(1)
    int prev1 = 2; // f(2)

    for (int i = 3; i <= n; i++) {
        int current = prev1 + prev2; // f(i) = f(i-1) + f(i-2)
        prev2 = prev1;
        prev1 = current;
    }

    return prev1;
}
```

## 25. 颜色分类 (Sort Colors) - 题目75

**问题描述：**
给定一个包含红色、白色和蓝色，一共n个元素的数组，原地对它们进行排序，使得相同颜色的元素相邻，并按照红色、白色、蓝色顺序排列。

```java
public void sortColors(int[] nums) {
    /**
     * 三指针法（荷兰国旗问题）
     * 时间复杂度: O(n)，空间复杂度: O(1)
     */
    int left = 0;              // 红色区域的右边界
    int right = nums.length - 1; // 蓝色区域的左边界
    int current = 0;           // 当前处理的位置

    while (current <= right) {
        if (nums[current] == 0) { // 红色
            swap(nums, left, current);
            left++;
            current++;
        } else if (nums[current] == 2) { // 蓝色
            swap(nums, current, right);
            right--;
            // 注意：这里current不自增，因为交换过来的元素还需要处理
        } else { // 白色
            current++;
        }
    }
}

private void swap(int[] nums, int i, int j) {
    int temp = nums[i];
    nums[i] = nums[j];
    nums[j] = temp;
}
```

## 26. 子集 (Subsets) - 题目78

**问题描述：**
给你一个整数数组nums，数组中的元素互不相同。返回该数组所有可能的子集（幂集）。

```java
import java.util.*;

public List<List<Integer>> subsets(int[] nums) {
    /**
     * 回溯法
     * 时间复杂度: O(n × 2^n)，空间复杂度: O(n × 2^n)
     */
    List<List<Integer>> result = new ArrayList<>();
    backtrack(nums, 0, new ArrayList<>(), result);
    return result;
}

private void backtrack(int[] nums, int start, List<Integer> currentSubset,
                      List<List<Integer>> result) {
    // 每次递归都添加当前子集
    result.add(new ArrayList<>(currentSubset));

    for (int i = start; i < nums.length; i++) {
        currentSubset.add(nums[i]);
        backtrack(nums, i + 1, currentSubset, result);
        currentSubset.remove(currentSubset.size() - 1); // 回溯
    }
}
```

## 27. 单词搜索 (Word Search) - 题目79

**问题描述：**
给定一个m x n二维字符网格board和一个字符串单词word。如果word存在于网格中，返回true；否则，返回false。

```java
public boolean exist(char[][] board, String word) {
    /**
     * 深度优先搜索 + 回溯
     * 时间复杂度: O(m × n × 4^L)，空间复杂度: O(L)
     * L是单词长度
     */
    int m = board.length;
    int n = board[0].length;

    for (int i = 0; i < m; i++) {
        for (int j = 0; j < n; j++) {
            if (dfs(board, word, i, j, 0)) {
                return true;
            }
        }
    }

    return false;
}

private boolean dfs(char[][] board, String word, int i, int j, int index) {
    // 如果已经匹配完整个单词
    if (index == word.length()) {
        return true;
    }

    // 边界检查和字符匹配检查
    if (i < 0 || i >= board.length || j < 0 || j >= board[0].length ||
        board[i][j] != word.charAt(index)) {
        return false;
    }

    // 标记当前位置已访问
    char temp = board[i][j];
    board[i][j] = '#';

    // 向四个方向搜索
    boolean found = dfs(board, word, i + 1, j, index + 1) ||
                   dfs(board, word, i - 1, j, index + 1) ||
                   dfs(board, word, i, j + 1, index + 1) ||
                   dfs(board, word, i, j - 1, index + 1);

    // 回溯：恢复当前位置的字符
    board[i][j] = temp;

    return found;
}
```

## 28. 柱状图中最大的矩形 (Largest Rectangle in Histogram) - 题目84

**问题描述：**
给定n个非负整数，用来表示柱状图中各个柱子的高度。每个柱子彼此相邻，且宽度为1。求在该柱状图中，能够勾勒出来的矩形的最大面积。

```java
import java.util.*;

public int largestRectangleArea(int[] heights) {
    /**
     * 单调栈
     * 时间复杂度: O(n)，空间复杂度: O(n)
     */
    Stack<Integer> stack = new Stack<>();
    int maxArea = 0;
    int n = heights.length;

    for (int i = 0; i <= n; i++) {
        int currentHeight = (i == n) ? 0 : heights[i];

        while (!stack.isEmpty() && heights[stack.peek()] > currentHeight) {
            int height = heights[stack.pop()];
            int width = stack.isEmpty() ? i : i - stack.peek() - 1;
            maxArea = Math.max(maxArea, height * width);
        }

        stack.push(i);
    }

    return maxArea;
}
```

## 29. 最大矩形 (Maximal Rectangle) - 题目85

**问题描述：**
给定一个仅包含0和1、大小为rows x cols的二维二进制矩阵，找出只包含1的最大矩形，并返回其面积。

```java
import java.util.*;

public int maximalRectangle(char[][] matrix) {
    /**
     * 基于柱状图最大矩形的解法
     * 时间复杂度: O(m × n)，空间复杂度: O(n)
     */
    if (matrix.length == 0 || matrix[0].length == 0) {
        return 0;
    }

    int m = matrix.length;
    int n = matrix[0].length;
    int[] heights = new int[n];
    int maxArea = 0;

    for (int i = 0; i < m; i++) {
        // 更新每一列的高度
        for (int j = 0; j < n; j++) {
            if (matrix[i][j] == '1') {
                heights[j]++;
            } else {
                heights[j] = 0;
            }
        }

        // 计算当前行作为底边的最大矩形面积
        maxArea = Math.max(maxArea, largestRectangleArea(heights));
    }

    return maxArea;
}

private int largestRectangleArea(int[] heights) {
    Stack<Integer> stack = new Stack<>();
    int maxArea = 0;
    int n = heights.length;

    for (int i = 0; i <= n; i++) {
        int currentHeight = (i == n) ? 0 : heights[i];

        while (!stack.isEmpty() && heights[stack.peek()] > currentHeight) {
            int height = heights[stack.pop()];
            int width = stack.isEmpty() ? i : i - stack.peek() - 1;
            maxArea = Math.max(maxArea, height * width);
        }

        stack.push(i);
    }

    return maxArea;
}
```

## 30. 二叉树的中序遍历 (Binary Tree Inorder Traversal) - 题目94

**问题描述：**
给定一个二叉树的根节点root，返回它的中序遍历。

```java
import java.util.*;

/**
 * Definition for a binary tree node.
 */
class TreeNode {
    int val;
    TreeNode left;
    TreeNode right;
    TreeNode() {}
    TreeNode(int val) { this.val = val; }
    TreeNode(int val, TreeNode left, TreeNode right) {
        this.val = val;
        this.left = left;
        this.right = right;
    }
}

public List<Integer> inorderTraversal(TreeNode root) {
    /**
     * 迭代法（使用栈）
     * 时间复杂度: O(n)，空间复杂度: O(n)
     */
    List<Integer> result = new ArrayList<>();
    Stack<TreeNode> stack = new Stack<>();
    TreeNode current = root;

    while (current != null || !stack.isEmpty()) {
        // 一直向左走到底
        while (current != null) {
            stack.push(current);
            current = current.left;
        }

        // 处理栈顶节点
        current = stack.pop();
        result.add(current.val);

        // 转向右子树
        current = current.right;
    }

    return result;
}
```

## 31. 不同的二叉搜索树 (Unique Binary Search Trees) - 题目96

**问题描述：**
给你一个整数n，求恰由n个节点组成且节点值从1到n互不相同的二叉搜索树有多少种？

```java
public int numTrees(int n) {
    /**
     * 动态规划（卡塔兰数）
     * 时间复杂度: O(n²)，空间复杂度: O(n)
     */
    int[] dp = new int[n + 1];
    dp[0] = 1; // 空树
    dp[1] = 1; // 只有一个节点

    for (int i = 2; i <= n; i++) {
        for (int j = 1; j <= i; j++) {
            // 以j为根节点，左子树有j-1个节点，右子树有i-j个节点
            dp[i] += dp[j - 1] * dp[i - j];
        }
    }

    return dp[n];
}
```

## 32. 验证二叉搜索树 (Validate Binary Search Tree) - 题目98

**问题描述：**
给你一个二叉树的根节点root，判断其是否是一个有效的二叉搜索树。

```java
public boolean isValidBST(TreeNode root) {
    /**
     * 递归法（设置上下界）
     * 时间复杂度: O(n)，空间复杂度: O(n)
     */
    return validate(root, null, null);
}

private boolean validate(TreeNode node, Integer lower, Integer upper) {
    if (node == null) {
        return true;
    }

    int val = node.val;

    // 检查当前节点值是否在有效范围内
    if (lower != null && val <= lower) {
        return false;
    }
    if (upper != null && val >= upper) {
        return false;
    }

    // 递归检查左右子树
    return validate(node.left, lower, val) && validate(node.right, val, upper);
}
```

## 33. 对称二叉树 (Symmetric Tree) - 题目101

**问题描述：**
给你一个二叉树的根节点root，检查它是否轴对称。

```java
public boolean isSymmetric(TreeNode root) {
    /**
     * 递归法
     * 时间复杂度: O(n)，空间复杂度: O(n)
     */
    return isMirror(root, root);
}

private boolean isMirror(TreeNode t1, TreeNode t2) {
    if (t1 == null && t2 == null) {
        return true;
    }
    if (t1 == null || t2 == null) {
        return false;
    }

    return (t1.val == t2.val) &&
           isMirror(t1.right, t2.left) &&
           isMirror(t1.left, t2.right);
}
```

## 34. 二叉树的层序遍历 (Binary Tree Level Order Traversal) - 题目102

**问题描述：**
给你二叉树的根节点root，返回其节点值的层序遍历。

```java
import java.util.*;

public List<List<Integer>> levelOrder(TreeNode root) {
    /**
     * 广度优先搜索（BFS）
     * 时间复杂度: O(n)，空间复杂度: O(n)
     */
    List<List<Integer>> result = new ArrayList<>();
    if (root == null) {
        return result;
    }

    Queue<TreeNode> queue = new LinkedList<>();
    queue.offer(root);

    while (!queue.isEmpty()) {
        int levelSize = queue.size();
        List<Integer> currentLevel = new ArrayList<>();

        for (int i = 0; i < levelSize; i++) {
            TreeNode node = queue.poll();
            currentLevel.add(node.val);

            if (node.left != null) {
                queue.offer(node.left);
            }
            if (node.right != null) {
                queue.offer(node.right);
            }
        }

        result.add(currentLevel);
    }

    return result;
}
```

## 35. 二叉树的锯齿形层序遍历 (Binary Tree Zigzag Level Order Traversal) - 题目103

**问题描述：**
给你二叉树的根节点root，返回其节点值的锯齿形层序遍历。

```java
import java.util.*;

public List<List<Integer>> zigzagLevelOrder(TreeNode root) {
    /**
     * BFS + 双端队列
     * 时间复杂度: O(n)，空间复杂度: O(n)
     */
    List<List<Integer>> result = new ArrayList<>();
    if (root == null) {
        return result;
    }

    Queue<TreeNode> queue = new LinkedList<>();
    queue.offer(root);
    boolean leftToRight = true;

    while (!queue.isEmpty()) {
        int levelSize = queue.size();
        Deque<Integer> currentLevel = new LinkedList<>();

        for (int i = 0; i < levelSize; i++) {
            TreeNode node = queue.poll();

            if (leftToRight) {
                currentLevel.offerLast(node.val);
            } else {
                currentLevel.offerFirst(node.val);
            }

            if (node.left != null) {
                queue.offer(node.left);
            }
            if (node.right != null) {
                queue.offer(node.right);
            }
        }

        result.add(new ArrayList<>(currentLevel));
        leftToRight = !leftToRight;
    }

    return result;
}
```

---

## 总结

以上35道题目涵盖了LeetCode Hot 100中最重要的中等难度题目，包括：

### 📚 算法分类

- **数组与字符串**：滑动窗口、双指针、排序等
- **链表操作**：合并、删除、相加等经典操作
- **回溯算法**：排列组合、子集、单词搜索等
- **动态规划**：路径问题、子数组问题、爬楼梯等
- **二分查找**：旋转数组、区间查找等
- **栈和队列**：括号匹配、柱状图、单调栈等
- **树的遍历**：前中后序、层序、锯齿形遍历等
- **贪心算法**：跳跃游戏、区间合并等
- **深度优先搜索**：单词搜索、树的验证等
- **广度优先搜索**：层序遍历等

### 🎯 解题技巧

1. **双指针技巧**：适用于数组、链表问题
2. **滑动窗口**：处理子串、子数组问题
3. **回溯模板**：系统性解决排列组合问题
4. **动态规划思维**：状态转移方程的建立
5. **单调栈应用**：解决柱状图类问题
6. **树的递归思维**：分治思想的体现

### 💡 学习建议

1. **按类型练习**：先掌握同一类型题目的解题模式
2. **理解算法本质**：不要死记硬背，要理解算法思想
3. **多写多练**：每道题至少独立完成3遍
4. **时间复杂度分析**：养成分析复杂度的习惯
5. **边界条件处理**：注意空值、单元素等特殊情况

每个解法都选择了最优雅高效的Java实现，并提供了详细的中文注释。建议结合实际编程练习，逐步掌握这些经典算法模式。
```
